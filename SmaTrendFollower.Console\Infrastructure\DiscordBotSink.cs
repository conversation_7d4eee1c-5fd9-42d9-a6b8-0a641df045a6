using Serilog.Core;
using Serilog.Events;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Infrastructure;

/// <summary>
/// Discord bot-token alert sink that sends Warning+ level log events to Discord
/// Replaces webhook sink with bot token authentication for better reliability
/// </summary>
public sealed class DiscordBotSink : ILogEventSink
{
    private readonly HttpClient _http = new();
    private readonly string _channel;
    private readonly IFormatProvider? _fmt;
    private readonly SemaphoreSlim _rateLimitSemaphore = new(1, 1); // Allow 1 message per second
    private DateTime _lastMessageTime = DateTime.MinValue;

    public DiscordBotSink(string token, string channel, IFormatProvider? fmt = null)
    {
        _channel = channel;
        _fmt = fmt;
        _http.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bot", token);
    }

    public void Emit(LogEvent logEvent)
    {
        if (logEvent.Level < LogEventLevel.Warning) return;   // send warn+error

        // Filter out repetitive or non-critical messages to reduce Discord noise
        var message = logEvent.RenderMessage();
        if (ShouldFilterMessage(message))
        {
            return;
        }

        // Fire and forget with rate limiting to prevent blocking
        _ = Task.Run(async () => await EmitAsync(logEvent));
    }

    private static bool ShouldFilterMessage(string message)
    {
        // Filter out common non-critical warnings that create Discord noise
        var filterPatterns = new[]
        {
            "Insufficient bar data for",           // ML training warnings
            "No data available for",               // Common data loading issues
            "Failed to load data for",             // Temporary data loading failures
            "Temporary file lock for",             // File I/O contention
            "being used by another process",       // File access conflicts
            "Cache contains 0 symbols",            // Empty cache warnings
            "Retrieved 0 symbols from cache",     // Cache miss warnings
            "Rate gate timeout for",              // Rate limiting warnings - handled by system
            "High semaphore utilization detected" // Semaphore monitoring warnings
        };

        return filterPatterns.Any(pattern => message.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private async Task EmitAsync(LogEvent logEvent)
    {
        try
        {
            // Rate limiting: ensure at least 1 second between messages
            await _rateLimitSemaphore.WaitAsync();
            try
            {
                var timeSinceLastMessage = DateTime.UtcNow - _lastMessageTime;
                if (timeSinceLastMessage < TimeSpan.FromSeconds(1))
                {
                    var delay = TimeSpan.FromSeconds(1) - timeSinceLastMessage;
                    await Task.Delay(delay);
                }
                _lastMessageTime = DateTime.UtcNow;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }

            var msg = logEvent.RenderMessage(_fmt);
            var payload = new { content = $"**[{logEvent.Level}]** {msg}" };
            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _http.PostAsync(
                $"https://discord.com/api/v10/channels/{_channel}/messages",
                content);

            if (response.IsSuccessStatusCode)
            {
                // Add metrics tracking
                MetricsRegistry.DiscordMessagesTotal
                    .WithLabels(logEvent.Level.ToString()).Inc();
            }
            else
            {
                // Don't log rate limit errors to avoid infinite loops
                if (response.StatusCode != System.Net.HttpStatusCode.TooManyRequests)
                {
                    System.Console.WriteLine($"[DiscordBotSink] Discord API returned {response.StatusCode}");
                }
                MetricsRegistry.DiscordSinkErrorsTotal.Inc();
            }
        }
        catch (Exception ex)
        {
            // Log errors to console to avoid infinite loops
            System.Console.WriteLine($"[DiscordBotSink] Error sending to Discord: {ex.Message}");
            MetricsRegistry.DiscordSinkErrorsTotal.Inc();
        }
    }
}


