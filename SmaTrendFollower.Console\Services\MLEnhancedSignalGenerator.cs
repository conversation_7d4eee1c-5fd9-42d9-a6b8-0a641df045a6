using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Console.Configuration;
using Alpaca.Markets;
using SmaTrendFollower.Models;
using SmaTrendFollower.MachineLearning.Prediction;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using System.Diagnostics;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Services;

/// <summary>
/// ML-enhanced signal generator that combines traditional technical analysis with machine learning.
/// Uses trained ML models to score and filter trading signals for improved performance.
/// </summary>
public sealed class MLEnhancedSignalGenerator : ISignalGenerator, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILiveStateStore _liveStateStore;
    private readonly IMomentumFilter _momentumFilter;
    private readonly IVolatilityFilter _volatilityFilter;
    private readonly IPositionSizer _positionSizer;
    private readonly ISignalRanker _signalRanker;
    private readonly ILogger<MLEnhancedSignalGenerator> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ParallelOptions _parallelOptions;
    private readonly SemaphoreSlim _rateGate = new(40); // FIXED: Increased to 40 concurrent calls to reduce timeouts
    private readonly TimeoutConfiguration _timeouts;
    private readonly float _mlProbabilityThreshold;

    public MLEnhancedSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILiveStateStore liveStateStore,
        IMomentumFilter momentumFilter,
        IVolatilityFilter volatilityFilter,
        IPositionSizer positionSizer,
        ISignalRanker signalRanker,
        ILogger<MLEnhancedSignalGenerator> logger,
        IServiceProvider serviceProvider,
        TimeoutConfiguration? timeouts = null,
        float mlProbabilityThreshold = 0.65f)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _liveStateStore = liveStateStore;
        _momentumFilter = momentumFilter;
        _volatilityFilter = volatilityFilter;
        _positionSizer = positionSizer;
        _signalRanker = signalRanker;
        _logger = logger;
        _serviceProvider = serviceProvider;
        _timeouts = timeouts ?? new TimeoutConfiguration();
        _mlProbabilityThreshold = mlProbabilityThreshold;

        _parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount,
            CancellationToken = CancellationToken.None
        };
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default)
    {
        var totalStopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting ML-enhanced signal generation (ML threshold: {Threshold:P1})", 
                _mlProbabilityThreshold);

            // Check if ML model is available
            if (!_signalRanker.IsModelLoaded)
            {
                _logger.LogWarning("ML model not loaded, falling back to traditional signal generation");
            }

            // Step 1: Check market conditions first
            var marketVolatility = await _volatilityFilter.GetMarketVolatilityAsync();
            if (!marketVolatility.IsEligible)
            {
                _logger.LogWarning("Market conditions unfavorable for trading: {Reason}", marketVolatility.Reason);
                return Enumerable.Empty<TradingSignal>();
            }

            _logger.LogInformation("Market conditions favorable: {Regime} volatility, VIX {VIX:F1}",
                marketVolatility.Regime, marketVolatility.VixLevel);

            // Step 2: Get universe and fetch data
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.Distinct().ToList();

            _logger.LogInformation("Screening {Count} symbols with ML-enhanced filtering", symbolList.Count);

            var symbolDataMap = await FetchDataInParallelAsync(symbolList, cancellationToken);
            _logger.LogInformation("Fetched data for {Count}/{Total} symbols", symbolDataMap.Count, symbolList.Count);

            // Step 3: Apply traditional filters and generate initial signals
            var traditionalSignals = ApplyTraditionalFilters(symbolDataMap, marketVolatility);
            _logger.LogInformation("Generated {Count} traditional signals", traditionalSignals.Count);

            if (!traditionalSignals.Any())
            {
                _logger.LogInformation("No traditional signals generated");
                return Enumerable.Empty<TradingSignal>();
            }

            // Step 4: Apply ML filtering and ranking
            var mlEnhancedSignals = ApplyMLFiltering(traditionalSignals, symbolDataMap, marketVolatility);
            _logger.LogInformation("ML filtering: {MLCount} signals passed threshold", mlEnhancedSignals.Count);

            // Step 5: Final ranking and selection
            var finalSignals = mlEnhancedSignals
                .Take(topN)
                .ToList();

            totalStopwatch.Stop();

            _logger.LogInformation("ML-enhanced signal generation completed: {Count} signals from {Total} symbols in {ElapsedMs:F0}ms",
                finalSignals.Count, symbolList.Count, totalStopwatch.Elapsed.TotalMilliseconds);

            // Log ML model info if available
            var modelInfo = _signalRanker.GetModelInfo();
            if (modelInfo != null)
            {
                _logger.LogInformation("ML model: {Version} (Accuracy: {Accuracy:P2}, Trained: {TrainedAt:yyyy-MM-dd})",
                    modelInfo.ModelVersion, modelInfo.Accuracy, modelInfo.TrainedAt);
            }

            return finalSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ML-enhanced signal generation");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    /// <summary>
    /// Fetches market data for all symbols using parallel async I/O with rate limiting
    /// </summary>
    private async Task<ConcurrentDictionary<string, List<IBar>>> FetchDataInParallelAsync(List<string> symbols, CancellationToken cancellationToken = default)
    {
        var dataStopwatch = Stopwatch.StartNew();
        var symbolDataMap = new ConcurrentDictionary<string, List<IBar>>();

        var fetchTasks = symbols.Select(async symbol =>
        {
            var sw = Stopwatch.StartNew();

            // FIXED: Reduced timeout to fail faster and prevent semaphore exhaustion
            var rateGateTimeout = TimeSpan.FromSeconds(30); // FIXED: Reduced to 30 seconds for faster failure detection
            using var rateGateCts = new CancellationTokenSource(rateGateTimeout);
            using var combinedRateGateCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, rateGateCts.Token);

            try
            {
                await _rateGate.WaitAsync(combinedRateGateCts.Token);
            }
            catch (OperationCanceledException) when (rateGateCts.Token.IsCancellationRequested)
            {
                _logger.LogWarning("Rate gate timeout for {Symbol} after {Timeout}s. Current semaphore count: {Count}/40. " +
                                 "This indicates slow API responses or network issues. Skipping symbol.",
                                 symbol, rateGateTimeout.TotalSeconds, _rateGate.CurrentCount);
                return; // Skip this symbol - don't fail the entire operation
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                _logger.LogDebug("Rate gate wait cancelled for {Symbol} due to overall cancellation", symbol);
                return; // Skip this symbol due to overall cancellation
            }

            try
            {
                // CRITICAL FIX: Use yesterday to avoid cache date range issues
                var endDate = DateTime.UtcNow.Date.AddDays(-1); // Use yesterday to avoid market data availability issues
                var startDate = endDate.AddDays(-300);

                // ADDITIONAL SAFETY: Ensure startDate is always before endDate
                if (startDate >= endDate)
                {
                    _logger.LogWarning("Invalid date range for {Symbol}: start {Start:yyyy-MM-dd} >= end {End:yyyy-MM-dd}, adjusting",
                        symbol, startDate, endDate);
                    startDate = endDate.AddDays(-300);
                    if (startDate >= endDate)
                    {
                        startDate = endDate.AddDays(-1); // Emergency fallback
                    }
                }

                // FIXED: Reduced timeout to prevent hanging requests and ensure proper cancellation
                using var marketDataCts = new CancellationTokenSource(TimeSpan.FromSeconds(25)); // FIXED: Reduced to 25 seconds to be less than HTTP timeout
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, marketDataCts.Token);
                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate).WaitAsync(combinedCts.Token);
                var bars = response.Items.ToList();

                if (bars.Count >= 200)
                {
                    symbolDataMap[symbol] = bars;
                }
                else
                {
                    _logger.LogDebug("Insufficient bars for {Symbol}: {Count} (need 200+)", symbol, bars.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch data for {Symbol}", symbol);
            }
            finally
            {
                _rateGate.Release();
                MetricsRegistry.SignalLatencyMs.Observe(sw.Elapsed.TotalMilliseconds);

                // FIXED: Add semaphore monitoring and alert on low availability
                if (sw.Elapsed.TotalSeconds > 30)
                {
                    _logger.LogWarning("Slow market data fetch for {Symbol}: {Duration}s. " +
                                     "Semaphore count after release: {Count}/40",
                                     symbol, sw.Elapsed.TotalSeconds, _rateGate.CurrentCount);
                }

                // Alert when semaphore utilization is high (less than 10 slots available)
                if (_rateGate.CurrentCount < 10)
                {
                    _logger.LogWarning("High semaphore utilization detected. Available slots: {Count}/40. " +
                                     "This may cause rate gate timeouts for other symbols.",
                                     _rateGate.CurrentCount);
                }
            }
        });

        using var timeoutCts = new CancellationTokenSource(_timeouts.MarketData.ParallelFetch);
        using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);
        try
        {
            await Task.WhenAll(fetchTasks).WaitAsync(combinedCts.Token);
        }
        catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
        {
            var successCount = symbolDataMap.Count;
            var totalCount = symbols.Count();
            _logger.LogWarning("Parallel data fetch timed out after {Timeout}ms. Successfully fetched {Success}/{Total} symbols. Continuing with partial data.",
                _timeouts.MarketData.ParallelFetch.TotalMilliseconds, successCount, totalCount);

            // Don't throw - continue with partial data
            if (successCount == 0)
            {
                _logger.LogError("No symbols successfully fetched - cannot continue signal generation");
                return new ConcurrentDictionary<string, List<IBar>>();
            }
        }

        dataStopwatch.Stop();
        _logger.LogInformation("Parallel data fetch completed in {ElapsedMs:F0}ms. " +
                              "Final semaphore count: {SemaphoreCount}/40",
                              dataStopwatch.Elapsed.TotalMilliseconds, _rateGate.CurrentCount);

        return symbolDataMap;
    }

    /// <summary>
    /// Applies traditional technical analysis filters
    /// </summary>
    private List<TradingSignal> ApplyTraditionalFilters(
        ConcurrentDictionary<string, List<IBar>> symbolDataMap,
        MarketVolatilityAnalysis marketVolatility)
    {
        var signals = new ConcurrentBag<TradingSignal>();

        Parallel.ForEach(symbolDataMap, kvp =>
        {
            var symbol = kvp.Key;
            var bars = kvp.Value;

            try
            {
                // Apply momentum filter
                var momentumEligible = _momentumFilter.IsEligible(symbol, bars);
                if (!momentumEligible)
                {
                    return;
                }

                // Apply volatility filter
                var volatilityAnalysis = _volatilityFilter.AnalyzeSymbolVolatility(symbol, bars);
                if (!volatilityAnalysis.IsEligible)
                {
                    return;
                }

                // Calculate technical indicators
                var currentPrice = bars.Last().Close;
                var sma50 = (decimal)bars.GetSma50();
                var sma200 = (decimal)bars.GetSma200();
                var atr14 = (decimal)bars.GetAtr14();
                var sixMonthReturn = (decimal)bars.GetTotalReturn(126);

                // Basic trend filter: close > sma50 && close > sma200
                if (currentPrice > sma50 && currentPrice > sma200 && atr14 > 0)
                {
                    var signal = new TradingSignal(symbol, currentPrice, atr14, sixMonthReturn);
                    signals.Add(signal);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing traditional filters for {Symbol}", symbol);
            }
        });

        return signals.ToList();
    }

    /// <summary>
    /// Applies ML filtering and ranking to traditional signals
    /// </summary>
    private List<TradingSignal> ApplyMLFiltering(
        List<TradingSignal> traditionalSignals,
        ConcurrentDictionary<string, List<IBar>> symbolDataMap,
        MarketVolatilityAnalysis marketVolatility)
    {
        if (!_signalRanker.IsModelLoaded)
        {
            _logger.LogWarning("ML model not loaded, returning traditional signals");
            return traditionalSignals.OrderByDescending(s => s.SixMonthReturn).ToList();
        }

        try
        {
            // Extract features for ML scoring
            var mlScoredSignals = _signalRanker.ScoreAndRank(traditionalSignals, signal =>
            {
                var bars = symbolDataMap[signal.Symbol];
                return ExtractSignalFeatures(signal, bars, marketVolatility);
            });

            // Filter by ML probability threshold and return ranked results
            var filteredSignals = mlScoredSignals
                .Where(x => x.Probability >= _mlProbabilityThreshold)
                .OrderByDescending(x => x.Score)
                .Select(x => x.Signal)
                .ToList();

            _logger.LogInformation("ML ranking: {FilteredCount}/{TotalCount} signals passed probability threshold {Threshold:P1}",
                filteredSignals.Count, traditionalSignals.Count, _mlProbabilityThreshold);

            return filteredSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ML filtering, falling back to traditional ranking");
            return traditionalSignals.OrderByDescending(s => s.SixMonthReturn).ToList();
        }
    }

    /// <summary>
    /// Extracts features for ML model from signal and market data
    /// </summary>
    private SignalFeatures ExtractSignalFeatures(
        TradingSignal signal,
        List<IBar> bars,
        MarketVolatilityAnalysis marketVolatility)
    {
        var currentPrice = bars.Last().Close;
        var sma50 = bars.GetSma50();
        var rsi14 = bars.GetRsi14();
        var volume20Avg = bars.TakeLast(20).Average(b => (double)b.Volume);
        var currentVolume = (double)bars.Last().Volume;

        // Get sentiment score for today (synchronous call for now)
        var sentiment = 0.0f;
        try
        {
            var momentumTrainer = _serviceProvider.GetService<IMomentumModelTrainer>();
            if (momentumTrainer != null)
            {
                sentiment = (float)momentumTrainer.GetSentimentScoreAsync(signal.Symbol, DateTime.UtcNow).GetAwaiter().GetResult();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get sentiment for {Symbol}, using neutral", signal.Symbol);
        }

        return new SignalFeatures(
            SmaGap: (float)(currentPrice / (decimal)sma50),
            Volatility: (float)(signal.Atr / signal.Price),
            Rsi: (float)rsi14,
            BreadthScore: 0.7f, // TODO: Implement breadth calculation
            VixLevel: (float)marketVolatility.VixLevel,
            SixMonthReturn: (float)signal.SixMonthReturn,
            RelativeVolume: (float)(currentVolume / volume20Avg),
            MarketRegime: marketVolatility.Regime switch
            {
                MarketVolatilityRegime.Low => 1.0f,
                MarketVolatilityRegime.Normal => 0.6f,
                MarketVolatilityRegime.Elevated => 0.4f,
                MarketVolatilityRegime.High => 0.3f,
                MarketVolatilityRegime.Crisis => 0.1f,
                _ => 0.5f
            },
            Sentiment: sentiment
        );
    }

    public void Dispose()
    {
        _rateGate?.Dispose();
        GC.SuppressFinalize(this);
    }
}
